<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.settings.name', 'Laravel') }}</title>
        <link rel="shortcut icon" href="{{ asset('images/favicon.png') }}" type="image/png">

        <!-- Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
        <link rel="preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" onload="this.onload=null;this.rel='stylesheet'" />

        <!-- Styles -->
        <link rel="preload" as="style" href="{{ asset('css/vendor.css') }}" onload="this.onload=null;this.rel='stylesheet'">
        <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="preload" as="style" onload="this.onload=null;this.rel='stylesheet'">
        @livewireStyles

        <!-- Scripts -->
        <script src="https://cdn.quilljs.com/1.3.6/quill.js"></script>
        <script src="{{ asset('js/app.js') }}" defer></script>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
            @livewire('navigation-dropdown')

            <!-- Page Heading -->
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>

            <!-- Page Content -->
            <main>
                <div class="mt-5 -mb-5 hidden annoucements" data-id="6">
                    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                        <div class="col-span-6">
                            <div class="w-full py-3 px-4 overflow-hidden sm:rounded-md flex items-center border bg-indigo-50 border-indigo-500">
                                <div class="text-indigo-500 w-10"> 
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                                    </svg>
                                </div>
                                <div class="ml-4 flex-1">
                                    <div class="text-sm text-gray-600 font-semibold">{{ __('Announcements') }}</div>
                                    <div class="text-md">
                                        {{ __('Lock your TMail with a Password') }} - 
                                        <a class="font-medium underline" href="{{ route('settings') }}">{{ __('Try Now') }}</a>
                                    </div>
                                </div>
                                <div class="close text-indigo-500 w-5 cursor-pointer"> 
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{ $slot }}
            </main>

            <footer class="flex justify-center items-center text-sm text-gray-600 space-x-3 pb-10">
                <span>{{ __('TMail') }}</span>
                <span class="bg-sky-200 text-sky-800 px-2 py-1 text-xs rounded-full">{{ config('app.settings.version') }}</span>
            </footer>
        </div>

        @stack('modals')

        @livewireScripts

        <script>
            const id = localStorage.getItem('annoucements');
            if(id) {
                const el = document.querySelector('.annoucements');
                if(el.dataset.id > id) {
                    el.classList.remove('hidden')
                }
            } else {
                document.querySelector('.annoucements').classList.remove('hidden')
            }
            document.querySelector('.annoucements .close') && document.querySelector('.annoucements .close').addEventListener('click', () => {
                localStorage.setItem('annoucements', document.querySelector('.annoucements').dataset.id);
                document.querySelector('.annoucements').remove();
            })
        </script>
        <script>
            let engine = "{{ config('app.settings.engine') }}";
            Livewire.on('engineUpdated', newEngine => {
                if(newEngine == 'imap') {
                    document.getElementById('imap-settings').style.display = ''
                } else {
                    document.getElementById('imap-settings').style.display = 'none'
                }
            })
            /* Export Settings from Admin Panel -> Settings -> Export/Import */
            Livewire.on('settingsExportFetched', settings => {
                let content = 'data:text/plain;charset=utf-8,' + btoa(settings);
                let link = document.createElement("a");
                link.setAttribute("href", content);
                link.setAttribute("download", "settings.tmail");
                document.body.appendChild(link);
                link.click();
                link.remove();
            });
            if(document.getElementById('import-settings')) {
                document.getElementById('import-settings').addEventListener('click', () => {
                    let reader = new FileReader();
                    reader.onload = (event) => {
                        let settings = atob(event.target.result);
                        if(settings) {
                            Livewire.emit('upload', settings);
                        }
                    };
                    reader.readAsText(document.querySelector('#import[type=file]').files[0]);
                });
                window.addEventListener('refresh', () => {
                    setTimeout(() => {
                        window.scrollTo(0, 0);
                        location.reload();
                    }, 1000);
                });
            }
        </script>
    </body>
</html>
