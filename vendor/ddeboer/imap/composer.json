{"name": "ddeboer/imap", "description": "Object-oriented IMAP for PHP", "license": "MIT", "keywords": ["email", "mail", "imap"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Community contributors", "homepage": "https://github.com/ddeboer/imap/graphs/contributors"}], "require": {"php": "~8.2.0 || ~8.3.0", "ext-dom": "*", "ext-iconv": "*", "ext-imap": "*", "ext-libxml": "*", "ext-mbstring": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.38.2", "laminas/laminas-mail": "^2.25.1", "phpstan/phpstan": "^1.10.43", "phpstan/phpstan-phpunit": "^1.3.15", "phpstan/phpstan-strict-rules": "^1.5.2", "phpunit/phpunit": "^10.4.2"}, "autoload": {"psr-4": {"Ddeboer\\Imap\\": "src/"}}, "autoload-dev": {"psr-4": {"Ddeboer\\Imap\\Tests\\": "tests/"}}}