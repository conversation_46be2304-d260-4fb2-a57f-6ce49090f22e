.reset * {
  all: revert;
}

.pause-spinner {
  animation-play-state: paused;
}

.bg-primary {
  background-color: var(--primary);
}

.text-primary {
  color: var(--primary);
}

.bg-secondary {
  background-color: var(--secondary);
}

.text-secondary {
  color: var(--secondary);
}

.bg-tertiary {
  background-color: var(--tertiary);
}

.text-tertiary {
  color: var(--tertiary);
}

.page h1,
.page h2,
.page h3,
.page h4,
.page h5,
.page h6,
.page ul,
.page ol,
.page li {
  all: revert;
}

.page.ql-editor {
  white-space: unset;
}

.groot-theme div.in-app-page #blogs .bg-gray-100 {
  background: #fff;
}

.adsbygoogle {
  min-width: 250px;
}

.groot-theme .ads-two .adsbygoogle,
.groot-theme .ads-three .adsbygoogle,
.nebula-theme .ads-two .adsbygoogle,
.nebula-theme .ads-three .adsbygoogle {
  min-width: 200px;
}

html :not(h1, h2, h3, h4, h5, h6) {
  font-family: var(--body-font);
}

html h1,
html h2,
html h3,
html h4,
html h5,
html h6 {
  font-family: var(--head-font) !important;
}

html h1 > *,
html h2 > *,
html h3 > *,
html h4 > *,
html h5 > *,
html h6 > * {
  font-family: var(--head-font) !important;
}

.has-tooltip .tooltip {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.3s;
  background: #000;
  color: #fff;
  padding: 5px 10px;
  font-size: 0.75em;
  border-radius: 0.5em;
}

.has-tooltip .tooltip::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: black transparent transparent transparent;
  transform: rotate(180deg);
}

.has-tooltip:hover .tooltip {
  visibility: visible;
  opacity: 1;
  transform: translateY(50px);
}

.has-tooltip.relative .tooltip {
  top: -2em;
}

@media only screen and (min-width: 400px) and (max-width: 1024px) {
  html {
    font-size: 14px;
  }
}

@media only screen and (max-width: 400px) {
  html {
    font-size: 12px;
  }
}
